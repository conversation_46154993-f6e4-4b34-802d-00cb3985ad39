{"servers": {"react-native.docs": {"type": "http", "url": "https://reactnative.dev/api/mcp", "description": "React Native official documentation and API reference"}, "react.docs": {"type": "http", "url": "https://react.dev/api/mcp", "description": "React official documentation, hooks, and patterns"}, "expo.docs": {"type": "http", "url": "https://docs.expo.dev/api/mcp", "description": "Expo SDK documentation and development tools"}, "typescript.docs": {"type": "http", "url": "https://www.typescriptlang.org/api/mcp", "description": "TypeScript language reference and best practices"}, "javascript.mdn": {"type": "http", "url": "https://developer.mozilla.org/api/mcp", "description": "MDN Web Docs for JavaScript, Web APIs, and standards"}}}