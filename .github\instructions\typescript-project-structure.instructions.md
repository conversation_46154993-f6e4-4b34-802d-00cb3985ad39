---
applyTo: '**/*.{ts,tsx,js,jsx,json}'
---

# TypeScript Project Structure

## Purpose
This instruction defines the recommended project structure and configuration for TypeScript projects to ensure consistency, maintainability, and proper tooling integration.

## Project Structure

### Recommended Directory Layout
```
project-root/
├── src/
│   ├── domain/           # Domain layer (business logic)
│   ├── application/      # Application layer (use cases)
│   ├── infrastructure/   # Infrastructure layer (external services)
│   ├── api/             # API layer (routes, controllers)
│   └── shared/          # Shared utilities and types
├── tests/
│   ├── unit/            # Unit tests
│   ├── integration/     # Integration tests
│   └── e2e/             # End-to-end tests
├── docs/                # Documentation
├── scripts/             # Build and deployment scripts
├── .github/             # GitHub workflows and templates
├── .vscode/             # VS Code settings
├── dist/                # Compiled output (gitignored)
├── node_modules/        # Dependencies (gitignored)
├── package.json         # Project configuration
├── tsconfig.json        # TypeScript configuration
├── jest.config.js       # Test configuration
├── .eslintrc.js         # ESLint configuration
├── .prettierrc          # Prettier configuration
└── README.md            # Project documentation
```

## Configuration Files

### TypeScript Configuration (tsconfig.json)
```json
{
  "compilerOptions": {
    "target": "ES2022",
    "module": "ESNext",
    "moduleResolution": "node",
    "lib": ["ES2022"],
    "outDir": "./dist",
    "rootDir": "./src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    "removeComments": false,
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "exactOptionalPropertyTypes": true,
    "baseUrl": "./src",
    "paths": {
      "@domain/*": ["domain/*"],
      "@application/*": ["application/*"],
      "@infrastructure/*": ["infrastructure/*"],
      "@api/*": ["api/*"],
      "@shared/*": ["shared/*"]
    }
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist", "tests"]
}
```

### Package.json Scripts
```json
{
  "scripts": {
    "build": "tsc",
    "build:watch": "tsc --watch",
    "start": "node dist/api/server.js",
    "dev": "ts-node-dev --respawn --transpile-only src/api/server.ts",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "lint": "eslint . --ext .ts,.tsx",
    "lint:fix": "eslint . --ext .ts,.tsx --fix",
    "format": "prettier --write .",
    "type-check": "tsc --noEmit",
    "clean": "rimraf dist"
  }
}
```

## File Naming Conventions

### General Rules
- Use kebab-case for file and directory names: `order-service.ts`
- Use PascalCase for class files: `OrderService` → `order-service.ts`
- Use camelCase for interface files: `IOrderRepository` → `order-repository.interface.ts`
- Use descriptive suffixes for different file types:
  - `.service.ts` for service classes
  - `.repository.ts` for repository implementations
  - `.interface.ts` for interface definitions
  - `.type.ts` for type definitions
  - `.dto.ts` for data transfer objects
  - `.entity.ts` for domain entities
  - `.value-object.ts` for value objects
  - `.event.ts` for domain events
  - `.test.ts` for test files
  - `.spec.ts` for specification files

### Examples
```
src/
  domain/
    order/
      order.entity.ts
      order-line.value-object.ts
      order-created.event.ts
      order.repository.interface.ts
  application/
    order/
      create-order.service.ts
      order.dto.ts
  infrastructure/
    order/
      order.repository.ts
      order.mapper.ts
  api/
    order/
      order.controller.ts
      order.routes.ts
```

## Import/Export Guidelines

### Barrel Exports
Use index.ts files to create clean import paths:

```typescript
// src/domain/order/index.ts
export { Order } from './order.entity';
export { OrderLine } from './order-line.value-object';
export { OrderCreatedEvent } from './order-created.event';
export type { IOrderRepository } from './order.repository.interface';

// Usage
import { Order, OrderLine, type IOrderRepository } from '@domain/order';
```

### Import Organization
Organize imports in the following order:
1. Node.js built-in modules
2. External libraries
3. Internal modules (using path mapping)
4. Relative imports

```typescript
// Node.js built-in
import { readFile } from 'fs/promises';

// External libraries
import express from 'express';
import { v4 as uuidv4 } from 'uuid';

// Internal modules
import { Order } from '@domain/order';
import { CreateOrderService } from '@application/order';

// Relative imports
import { OrderMapper } from './order.mapper';
```

## Best Practices

1. **Consistent Structure**: Follow the defined directory structure across all projects
2. **Path Mapping**: Use TypeScript path mapping for cleaner imports
3. **Barrel Exports**: Use index.ts files to simplify imports
4. **Type Safety**: Enable strict TypeScript settings
5. **Documentation**: Include README.md files in major directories
6. **Testing**: Maintain parallel test structure to source code
7. **Configuration**: Keep all configuration files in the project root
8. **Environment**: Use environment-specific configuration files

## References
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Node.js Best Practices](https://github.com/goldbergyoni/nodebestpractices)
