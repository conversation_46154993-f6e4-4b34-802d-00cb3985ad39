---
applyTo: '**/*.{ts,tsx,js,jsx}'
---

# TypeScript Coding Style

## General Guidelines
- Follow the official TypeScript coding conventions: https://www.typescriptlang.org/docs/handbook/declaration-files/do-s-and-don-ts.html
- Use ESLint and Prettier for consistent code formatting
- Prefer clarity and readability over brevity
- Use consistent formatting and naming throughout the codebase

## Naming Conventions
- Use `PascalCase` for class, interface, type, and enum names
- Use `camelCase` for variable, function, and method names
- Use `SCREAMING_SNAKE_CASE` for constants
- Prefix interfaces with `I` when needed for disambiguation (e.g., `IOrderService`)
- Use meaningful, descriptive names; avoid abbreviations

## Formatting
- Use 2 spaces for indentation (no tabs)
- Use semicolons consistently
- Add a blank line between function definitions
- Use trailing commas in multi-line objects and arrays
- Prefer single quotes for strings unless interpolation is needed

## Type Declarations
- Use explicit types for function parameters and return values
- Use `const` for immutable values, `let` for mutable variables
- Avoid `any` type; use `unknown` or specific types instead
- Use type assertions sparingly and prefer type guards

### Example
```typescript
// Before
let x = 1;
let y = 2.0;
let z = "Hello";
let item = new ProductBacklogItem("Test", "Test", 1, 1, 1);

// After
const x: number = 1;
const y: number = 2.0;
const z: string = "Hello";
const item: ProductBacklogItem = new ProductBacklogItem("Test", "Test", 1, 1, 1);
```

## Classes and Interfaces
- Make classes `readonly` for immutable properties
- Use `private` and `protected` modifiers appropriately
- Implement interfaces explicitly when needed

## Error Handling
- Use custom error classes for domain-specific errors
- Prefer throwing errors over returning null/undefined
- Use type guards for runtime type checking

### Example
```typescript
// Before
throw new Error("parameterName is invalid");

// After
throw new InvalidParameterError("parameterName", value);
```

## Code Structure
- One class/interface/type per file when possible
- Organize files by feature/domain when possible
- Group imports at the top: external libraries first, then internal modules
- Use barrel exports (index.ts) for clean imports
- Use path mapping for cleaner import statements

## Comments & Documentation
- Use JSDoc comments (`/** */`) for public APIs
- Write comments to explain why, not what, when necessary
- Remove commented-out code before committing

## Modern TypeScript Features
- Use optional chaining (`?.`) and nullish coalescing (`??`)
- Prefer template literals over string concatenation
- Use destructuring for object and array access
- Use async/await over Promise chains

## Testing
- Use Jest or Vitest for testing framework
- Follow the AAA pattern (Arrange, Act, Assert)
- Use descriptive test names that explain the scenario

# References
- Follow TypeScript's [coding guidelines](https://www.typescriptlang.org/docs/handbook/declaration-files/do-s-and-don-ts.html)
- ESLint TypeScript rules: https://typescript-eslint.io/rules/
