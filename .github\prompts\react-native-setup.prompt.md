---
description: Complete setup guide for React Native project with TypeScript, testing, and development tools
mode: agent
---

# React Native Project Setup Guide

This prompt guides you through setting up a complete React Native project with TypeScript, testing framework, linting, and essential development tools.

## Prerequisites

Ensure you have the following installed:

- Node.js (v18 or later)
- npm, yarn, or pnpm
- React Native CLI or Expo CLI
- Android Studio (for Android development)
- Xcode (for iOS development, macOS only)
- Git

## Project Initialization

### Option 1: React Native CLI (Recommended for native modules)

```bash
npx react-native@latest init MyApp --template react-native-template-typescript
cd MyApp
```

### Option 2: Expo CLI (Recommended for rapid prototyping)

```bash
npx create-expo-app MyApp --template
cd MyApp
```

## Essential Dependencies

### Development Dependencies

```bash
# Testing
npm install --save-dev jest @testing-library/react-native @testing-library/jest-native

# Linting and Formatting
npm install --save-dev eslint @typescript-eslint/eslint-plugin @typescript-eslint/parser
npm install --save-dev prettier eslint-plugin-prettier eslint-config-prettier
npm install --save-dev eslint-plugin-react eslint-plugin-react-hooks eslint-plugin-react-native

# Git Hooks
npm install --save-dev husky lint-staged

# Type Checking
npm install --save-dev @types/react @types/react-native
```

### Production Dependencies

```bash
# Navigation
npm install @react-navigation/native @react-navigation/stack @react-navigation/bottom-tabs
npm install react-native-screens react-native-safe-area-context

# State Management (choose one)
npm install @reduxjs/toolkit react-redux  # Redux Toolkit
# OR
npm install zustand  # Zustand (lighter alternative)

# HTTP Client
npm install axios
# OR
npm install @tanstack/react-query  # For server state management

# UI Components (choose one)
npm install react-native-elements react-native-vector-icons
# OR
npm install native-base
# OR
npm install @ui-kitten/components react-native-eva-icons
```

## Configuration Files

### TypeScript Configuration (tsconfig.json)

```json
{
  "extends": "@react-native/typescript-config/tsconfig.json",
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "exactOptionalPropertyTypes": true,
    "baseUrl": "./src",
    "paths": {
      "@components/*": ["components/*"],
      "@screens/*": ["screens/*"],
      "@services/*": ["services/*"],
      "@utils/*": ["utils/*"],
      "@types/*": ["types/*"],
      "@hooks/*": ["hooks/*"],
      "@navigation/*": ["navigation/*"],
      "@assets/*": ["../assets/*"]
    }
  },
  "include": ["src/**/*", "index.js"],
  "exclude": ["node_modules", "android", "ios"]
}
```

### ESLint Configuration (.eslintrc.js)

```javascript
module.exports = {
  root: true,
  extends: [
    '@react-native',
    '@typescript-eslint/recommended',
    'prettier',
  ],
  parser: '@typescript-eslint/parser',
  plugins: ['@typescript-eslint', 'react', 'react-hooks', 'react-native'],
  rules: {
    'react-hooks/rules-of-hooks': 'error',
    'react-hooks/exhaustive-deps': 'warn',
    'react-native/no-unused-styles': 'error',
    'react-native/split-platform-components': 'error',
    'react-native/no-inline-styles': 'warn',
    'react-native/no-color-literals': 'warn',
    '@typescript-eslint/no-unused-vars': 'error',
    '@typescript-eslint/explicit-function-return-type': 'warn',
  },
};
```

### Prettier Configuration (.prettierrc)

```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false,
  "bracketSpacing": true,
  "arrowParens": "avoid"
}
```

### Jest Configuration (jest.config.js)

```javascript
module.exports = {
  preset: 'react-native',
  setupFilesAfterEnv: ['<rootDir>/src/test-setup.ts'],
  transformIgnorePatterns: [
    'node_modules/(?!(react-native|@react-native|@react-navigation)/)',
  ],
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/test-setup.ts',
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
};
```

## Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── common/         # Generic components (Button, Input, etc.)
│   ├── forms/          # Form-specific components
│   └── index.ts        # Barrel exports
├── screens/            # Screen components
│   ├── auth/           # Authentication screens
│   ├── home/           # Home-related screens
│   └── profile/        # Profile-related screens
├── navigation/         # Navigation configuration
│   ├── AppNavigator.tsx
│   ├── AuthNavigator.tsx
│   └── types.ts
├── services/           # API and business logic
│   ├── api/            # API clients
│   ├── auth/           # Authentication service
│   └── storage/        # Local storage utilities
├── hooks/              # Custom React hooks
├── utils/              # Utility functions
├── types/              # TypeScript type definitions
├── constants/          # App constants
├── store/              # State management (Redux/Zustand)
└── assets/             # Images, fonts, etc.
```

## Package.json Scripts

```json
{
  "scripts": {
    "android": "react-native run-android",
    "ios": "react-native run-ios",
    "start": "react-native start",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "lint": "eslint . --ext .ts,.tsx,.js,.jsx",
    "lint:fix": "eslint . --ext .ts,.tsx,.js,.jsx --fix",
    "format": "prettier --write .",
    "type-check": "tsc --noEmit",
    "clean": "react-native clean",
    "prepare": "husky install"
  }
}
```

## Git Hooks Setup

```bash
# Initialize Husky
npx husky install

# Add pre-commit hook
npx husky add .husky/pre-commit "npx lint-staged"

# Add commit message hook
npx husky add .husky/commit-msg "npx commitlint --edit $1"
```

### Lint-staged Configuration (package.json)

```json
{
  "lint-staged": {
    "*.{ts,tsx,js,jsx}": [
      "eslint --fix",
      "prettier --write"
    ],
    "*.{json,md,yml,yaml}": [
      "prettier --write"
    ]
  }
}
```

## Environment Setup

### Development Environment Variables (.env)

```bash
API_BASE_URL=https://api.development.com
DEBUG_MODE=true
```

### Production Environment Variables (.env.production)

```bash
API_BASE_URL=https://api.production.com
DEBUG_MODE=false
```

## Testing Setup

Create `src/test-setup.ts`:

```typescript
import '@testing-library/jest-native/extend-expect';

// Mock react-native modules
jest.mock('react-native/Libraries/Animated/NativeAnimatedHelper');

// Mock navigation
jest.mock('@react-navigation/native', () => ({
  useNavigation: () => ({
    navigate: jest.fn(),
    goBack: jest.fn(),
  }),
  useRoute: () => ({
    params: {},
  }),
}));
```

## Final Steps

1. **Install iOS dependencies** (macOS only):
   ```bash
   cd ios && pod install && cd ..
   ```

2. **Run the project**:
   ```bash
   npm run android  # For Android
   npm run ios      # For iOS
   ```

3. **Verify setup**:
   ```bash
   npm run lint     # Check linting
   npm run test     # Run tests
   npm run type-check  # Check TypeScript
   ```

## Additional Recommendations

- Set up CI/CD pipeline with GitHub Actions
- Configure code signing for iOS
- Set up crash reporting (Crashlytics, Sentry)
- Implement analytics (Firebase Analytics, Amplitude)
- Configure deep linking
- Set up push notifications
- Implement proper error boundaries
- Add accessibility testing

## References

- [React Native Documentation](https://reactnative.dev/)
- [React Navigation](https://reactnavigation.org/)
- [Testing Library](https://testing-library.com/docs/react-native-testing-library/intro/)
