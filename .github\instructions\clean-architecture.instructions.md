---
applyTo: '**/*.{ts,tsx,js,jsx}'
---

# Clean Architecture

When implementing backend services, follow these Clean Architecture principles to ensure maintainability, scalability, and separation of concerns. This rule is tailored for TypeScript/Node.js solutions with a modular structure.

## 1. Solution Structure

- The solution **must** be organized into four main modules (one per layer):
  - `src/domain/` (core business logic, entities, value objects, domain events)
  - `src/application/` (use cases, commands, queries, interfaces for external services)
  - `src/infrastructure/` (implementations for external services, database access, third-party integrations)
  - `src/api/` (API endpoints, Express routes, request/response models)
- Each module must contain a marker/reference file (e.g., `domain-reference.ts`) for test discovery and architecture validation.
- Tests must be in separate directories:
  - `tests/unit/` (for Domain and Application)
  - `tests/integration/` (for Infrastructure, Api, and architecture validation)

## 2. Dependencies Between Layers

- **Domain**: has no dependencies.
- **Application**: depends only on **Domain**.
- **Infrastructure**: depends on **Application** and **Domain**.
- **Api**: depends only on **Infrastructure**.
- These dependencies **must** be enforced by automated architecture tests (e.g., using dependency-cruiser in `architecture.test.ts`).
- Forbidden dependencies (e.g., database drivers in Api/Domain) must be checked by tests.

## 3. Folder and File Structure

- Use a **feature-oriented** (domain-driven) folder structure in each layer (e.g., `Order/`, `Customer/`).
- Do **not** use technical root folders (Entities, ValueObjects, Services, etc.).
- Example minimal structure:

```
src/
  domain/
    domain-reference.ts
    order/
      order.ts
      order-created-event.ts
    customer/
      customer.ts
  application/
    application-reference.ts
    order/
      ...
    customer/
      ...
  infrastructure/
    infrastructure-reference.ts
    order/
      ...
    customer/
      ...
  api/
    server.ts
    ...
tests/
  unit/
    ...
  integration/
    architecture.test.ts
    ...
```

## 4. Coding Style and Conventions

- Use ES modules with explicit imports/exports.
- One class/interface/type per file.
- Follow TypeScript coding conventions.
- Organize files by feature/domain.

## 5. Implementation Guidelines

- **Domain Layer**: All business logic, entities, value objects, and domain events. No dependencies on other layers.
- **Application Layer**: Use cases, commands, queries, interfaces for repositories/services. No business logic.
- **Infrastructure Layer**: Implementations for interfaces, database access, external integrations. No business logic.
- **Api Layer**: Minimal API endpoints, request/response mapping. No business logic.
- Use dependency injection for all cross-layer dependencies.
- Avoid circular dependencies.
- Do not use a mediator library; call service methods directly from the Api layer.

## 6. Testing and Architecture Validation

- **Unit Tests**: In `tests/unit/`, for Domain and Application layers only. Use Jest/Vitest and mock functions for mocks.
- **Integration Tests**: In `tests/integration/`, for Infrastructure and Api layers. Use Testcontainers/Microcks for advanced scenarios.
- **Architecture Tests**: Must be present in `architecture.test.ts` and:
  - Enforce allowed/forbidden dependencies between layers
  - Check for forbidden dependencies (e.g., database drivers in Api/Domain)
  - Optionally, check for immutability in Domain
- Always write tests before implementation (TDD).

## 7. Architecture Testing Example

To enforce and validate architecture rules, add automated tests in `tests/integration/architecture.test.ts` using [dependency-cruiser](https://github.com/sverweij/dependency-cruiser). Example:

```typescript
import { readFileSync } from 'fs';
import { join } from 'path';
import { cruise } from 'dependency-cruiser';

describe('Architecture Tests', () => {
  const srcPath = join(__dirname, '../../src');

  test('Domain layer should not depend on other layers', async () => {
    const result = cruise(['src/domain'], {
      forbidden: [
        {
          name: 'domain-no-application-deps',
          from: { path: '^src/domain' },
          to: { path: '^src/application' },
        },
        {
          name: 'domain-no-infrastructure-deps',
          from: { path: '^src/domain' },
          to: { path: '^src/infrastructure' },
        },
        {
          name: 'domain-no-api-deps',
          from: { path: '^src/domain' },
          to: { path: '^src/api' },
        },
      ],
    });

    expect(result.summary.violations).toBe(0);
  });

  test('Application layer should only depend on Domain', async () => {
    const result = cruise(['src/application'], {
      forbidden: [
        {
          name: 'application-no-infrastructure-deps',
          from: { path: '^src/application' },
          to: { path: '^src/infrastructure' },
        },
        {
          name: 'application-no-api-deps',
          from: { path: '^src/application' },
          to: { path: '^src/api' },
        },
      ],
    });

    expect(result.summary.violations).toBe(0);
  });

  test('API layer should not depend on Infrastructure directly', async () => {
    const result = cruise(['src/api'], {
      forbidden: [
        {
          name: 'api-no-infrastructure-deps',
          from: { path: '^src/api' },
          to: { path: '^src/infrastructure' },
        },
      ],
    });

    expect(result.summary.violations).toBe(0);
  });

  test('No database drivers in Domain or API layers', async () => {
    const result = cruise(['src'], {
      forbidden: [
        {
          name: 'no-db-in-domain',
          from: { path: '^src/domain' },
          to: { path: 'node_modules/(mongodb|mysql|pg|sqlite)' },
        },
        {
          name: 'no-db-in-api',
          from: { path: '^src/api' },
          to: { path: 'node_modules/(mongodb|mysql|pg|sqlite)' },
        },
      ],
    });

    expect(result.summary.violations).toBe(0);
  });
});
```

- Install dependency-cruiser: `npm install --save-dev dependency-cruiser`
- Add tests to check for forbidden dependencies (e.g., database drivers in Api/Domain)
- Run these tests with `npm test` to ensure architecture rules are enforced after every change.

## Additional Guidelines

1. Use dependency injection to manage dependencies across layers.
2. Avoid circular dependencies between layers.
3. Write unit tests for **Domain** and **Application** layers.
4. Use integration tests for **Infrastructure** and **Api** layers.
5. Follow SOLID principles within each layer.
6. Avoid using a mediator library; instead, directly call service methods from the **Api** layer.

# References
- [Clean Architecture](https://blog.cleancoder.com/uncle-bob/2012/08/13/TheCleanArchitecture.html)
