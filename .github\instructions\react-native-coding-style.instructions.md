---
applyTo: '**/*.{ts,tsx,js,jsx}'
description: Enforces React Native specific coding style and best practices for mobile app development
---

# React Native Coding Style

## General Guidelines
- Follow React Native best practices: https://reactnative.dev/docs/performance
- Use TypeScript for type safety and better development experience
- Follow React hooks patterns and functional components
- Optimize for mobile performance and user experience
- Use platform-specific code when necessary

## Component Structure
- Use functional components with hooks instead of class components
- Prefer composition over inheritance
- Keep components small and focused on a single responsibility
- Use custom hooks to extract and reuse stateful logic

### Example Component Structure
```typescript
// components/UserProfile.tsx
import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';

interface UserProfileProps {
  userId: string;
  onUserLoad?: (user: User) => void;
}

export const UserProfile: React.FC<UserProfileProps> = ({ userId, onUserLoad }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadUser(userId);
  }, [userId]);

  const loadUser = async (id: string) => {
    try {
      setLoading(true);
      const userData = await userService.getUser(id);
      setUser(userData);
      onUserLoad?.(userData);
    } catch (error) {
      console.error('Failed to load user:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <View style={styles.container}>
      <Text style={styles.name}>{user?.name}</Text>
      <Text style={styles.email}>{user?.email}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#fff',
  },
  name: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  email: {
    fontSize: 14,
    color: '#666',
  },
});
```

## Styling Guidelines
- Use StyleSheet.create() for performance optimization
- Follow consistent naming conventions for styles
- Use platform-specific styles when needed
- Prefer flexbox for layouts
- Use responsive design principles

### Style Organization
```typescript
const styles = StyleSheet.create({
  // Container styles first
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  // Layout styles
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  // Text styles
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  // Platform-specific styles
  shadow: Platform.select({
    ios: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    android: {
      elevation: 4,
    },
  }),
});
```

## Performance Best Practices
- Use React.memo() for expensive components
- Implement proper key props for lists
- Use FlatList/SectionList for large datasets
- Optimize images with proper sizing and formats
- Use lazy loading for heavy components

### FlatList Optimization
```typescript
const UserList: React.FC<{ users: User[] }> = ({ users }) => {
  const renderUser = useCallback(({ item }: { item: User }) => (
    <UserItem key={item.id} user={item} />
  ), []);

  const keyExtractor = useCallback((item: User) => item.id, []);

  return (
    <FlatList
      data={users}
      renderItem={renderUser}
      keyExtractor={keyExtractor}
      removeClippedSubviews={true}
      maxToRenderPerBatch={10}
      windowSize={10}
      initialNumToRender={10}
    />
  );
};
```

## Navigation Patterns
- Use React Navigation for navigation
- Implement proper type safety for navigation
- Use proper navigation patterns (stack, tab, drawer)
- Handle deep linking appropriately

### Navigation Types
```typescript
// types/navigation.ts
export type RootStackParamList = {
  Home: undefined;
  Profile: { userId: string };
  Settings: undefined;
};

export type HomeScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'Home'
>;

export type ProfileScreenRouteProp = RouteProp<RootStackParamList, 'Profile'>;
```

## State Management
- Use React Context for simple global state
- Consider Redux Toolkit for complex state management
- Use React Query/SWR for server state
- Keep local state minimal and focused

## Error Handling
- Implement proper error boundaries
- Use try-catch blocks for async operations
- Provide meaningful error messages to users
- Log errors appropriately for debugging

### Error Boundary Example
```typescript
class ErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(): { hasError: boolean } {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return <ErrorScreen onRetry={() => this.setState({ hasError: false })} />;
    }

    return this.props.children;
  }
}
```

## Testing Guidelines
- Write unit tests for business logic
- Use React Native Testing Library for component tests
- Test user interactions and accessibility
- Mock platform-specific APIs appropriately

## File Organization
```
src/
├── components/          # Reusable UI components
│   ├── common/         # Generic components
│   └── forms/          # Form-specific components
├── screens/            # Screen components
├── navigation/         # Navigation configuration
├── services/           # API and business logic
├── hooks/              # Custom React hooks
├── utils/              # Utility functions
├── types/              # TypeScript type definitions
├── constants/          # App constants
└── assets/             # Images, fonts, etc.
```

## Platform-Specific Code
- Use Platform.select() for simple platform differences
- Create separate .ios.tsx and .android.tsx files for complex differences
- Test on both platforms regularly

## Accessibility
- Use accessibilityLabel and accessibilityHint
- Implement proper focus management
- Test with screen readers
- Ensure sufficient color contrast

## References
- [React Native Performance](https://reactnative.dev/docs/performance)
- [React Navigation](https://reactnavigation.org/)
- [React Native Testing Library](https://callstack.github.io/react-native-testing-library/)
