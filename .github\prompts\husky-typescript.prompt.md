---
description: Installation and Initialization of <PERSON><PERSON> for TypeScript/Node.js
mode: agent
---

# Installation and Initialization of <PERSON><PERSON> for TypeScript/Node.js

Husky is a tool that helps you manage Git hooks easily. Below are the steps to install and initialize <PERSON><PERSON> for your TypeScript/Node.js project.

## Prerequisites

Ensure you have the following installed:

- Node.js and npm (or yarn/pnpm)
- Git
- TypeScript (installed globally or as dev dependency)

## Steps

1. **Install Husky**

   Run the following command to install <PERSON><PERSON>:

   ```bash
   npm install --save-dev husky
   # or
   yarn add --dev husky
   # or
   pnpm add --save-dev husky
   ```

2. **Enable Git Hooks**

   Initialize <PERSON>sky to enable Git hooks:

   ```bash
   npx husky install
   # or
   yarn husky install
   # or
   pnpm exec husky install
   ```

   This will create a `.husky/` directory in your project.

   Add the following script to your `package.json`:

   ```json
   {
     "scripts": {
       "prepare": "husky install"
     }
   }
   ```

3. **Add a Commit Message Hook**

   Create a commit message hook to lint commit messages:

   ```bash
   npx husky add .husky/commit-msg 'npx commitlint --edit $1'
   ```

   Install commitlint:

   ```bash
   npm install --save-dev @commitlint/config-conventional @commitlint/cli
   ```

   Create a `commitlint.config.js` file:

   ```javascript
   module.exports = {
     extends: ['@commitlint/config-conventional'],
     rules: {
       'type-enum': [
         2,
         'always',
         [
           'build',
           'feat',
           'ci',
           'chore',
           'docs',
           'fix',
           'perf',
           'refactor',
           'revert',
           'style',
           'test'
         ]
       ]
     }
   };
   ```

4. **Add a Pre-Commit Hook**

   Create a pre-commit hook to format and lint staged files before committing:

   ```bash
   npx husky add .husky/pre-commit 'npx lint-staged'
   ```

   Install lint-staged and prettier:

   ```bash
   npm install --save-dev lint-staged prettier eslint
   ```

   Add lint-staged configuration to your `package.json`:

   ```json
   {
     "lint-staged": {
       "*.{ts,tsx,js,jsx}": [
         "eslint --fix",
         "prettier --write"
       ],
       "*.{json,md,yml,yaml}": [
         "prettier --write"
       ]
     }
   }
   ```

5. **Add a Pre-Push Hook (Optional)**

   Create a pre-push hook to run tests before pushing:

   ```bash
   npx husky add .husky/pre-push 'npm test'
   ```

6. **Validate Husky Installation**

   To ensure Husky is properly installed and the commit message hook is working:

   1. Switch to a temporary branch:

      ```bash
      git checkout -b temp-validation-branch
      ```

   2. Make a commit with an invalid commit message (should fail):

      ```bash
      echo "Test file" > test.txt
      git add test.txt
      git commit -m "invalid message"
      ```

      If the commit is rejected due to the invalid message, Husky is working correctly.

   3. Make a commit with a valid conventional message (should succeed):

      ```bash
      git commit -m "feat(test): add test file"
      ```

   4. Switch back to your original branch:

      ```bash
      git checkout main
      # or your default branch name
      ```

   5. Delete the temporary branch:

      ```bash
      git branch -D temp-validation-branch
      ```

7. **Verify Installation**

   Check that Husky is working by making a commit. The pre-commit hook should run automatically.

## Additional Notes

- You can customize hooks by editing the scripts in the `.husky/` directory
- Ensure your team members run `npm install` after cloning the repository to set up Husky
- Consider adding TypeScript compilation check to pre-commit: `npx tsc --noEmit`

## Example Package.json Scripts

```json
{
  "scripts": {
    "prepare": "husky install",
    "test": "jest",
    "test:watch": "jest --watch",
    "lint": "eslint . --ext .ts,.tsx,.js,.jsx",
    "lint:fix": "eslint . --ext .ts,.tsx,.js,.jsx --fix",
    "format": "prettier --write .",
    "type-check": "tsc --noEmit"
  }
}
```
